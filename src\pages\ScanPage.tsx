import React, { useEffect, useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, MapPin, Calendar, Smartphone, Package, ArrowLeft } from "lucide-react";
import { getMapScans } from "@/lib/api/methods";
import { MapScanData } from "@/lib/api/types";
import WorldMap from "@/components/WorldMap";

interface ScanDetails {
  id: string;
  result: "genuine" | "tampered";
  scanId: string;
  scanDate: string;
  location: string;
  coordinates?: string;
  latitude?: number;
  longitude?: number;
  qrSerialNo: string;
  product: string;
  deviceDetails: string;
}

const ScanPage: React.FC = () => {
  const { scanId } = useParams<{ scanId: string }>();
  const navigate = useNavigate();
  const [scanDetails, setScanDetails] = useState<ScanDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchScanDetails = async () => {
      if (!scanId) {
        setError("No scan ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        // Fetch all scans and find the specific one
        // In a real app, you'd have an API endpoint for a single scan
        const response = await getMapScans();
        const scan = response.data.find((s: MapScanData) => s.scan_id === scanId);

        if (!scan) {
          setError("Scan not found");
          setLoading(false);
          return;
        }

        // Convert scan data to the format expected by the component
        const details: ScanDetails = {
          id: scan.scan_id,
          result: scan.result.value === 1 ? "genuine" : "tampered",
          scanId: scan.scan_id,
          scanDate: new Date(scan.scan_time).toLocaleString(),
          location: scan.location,
          coordinates: `${scan.latitude}, ${scan.longitude}`,
          latitude: scan.latitude,
          longitude: scan.longitude,
          qrSerialNo: scan.qr_serial_number,
          product: scan.product_name,
          deviceDetails: "Device details not available"
        };

        setScanDetails(details);
      } catch (err) {
        setError("Failed to fetch scan details");
        console.error("Error fetching scan details:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchScanDetails();
  }, [scanId]);

  // Helper function to open Google Maps
  const openGoogleMaps = () => {
    if (scanDetails?.coordinates) {
      const url = `https://www.google.com/maps?q=${scanDetails.coordinates}`;
      window.open(url, "_blank");
    }
  };

  if (loading) {
    return (
      <div className="space-y-6 p-6 bg-gray-50 min-h-screen">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => navigate(-1)} className="p-2">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Loading scan details...</h1>
        </div>
      </div>
    );
  }

  if (error || !scanDetails) {
    return (
      <div className="space-y-6 p-6 bg-gray-50 min-h-screen">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => navigate(-1)} className="p-2">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Error</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <p className="text-red-600">{error || "Scan details not found"}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const isGenuine = scanDetails.result === "genuine";

  return (
    <div className="space-y-6 p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" onClick={() => navigate(-1)} className="p-2">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-2xl font-bold">Scan #{scanDetails.id}</h1>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Scan Details Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              <span>Scan Details</span>
              <div className="flex items-center space-x-2">
                {isGenuine ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-500" />
                )}
                <span className={`font-semibold ${isGenuine ? "text-green-600" : "text-red-600"}`}>
                  {isGenuine ? "Genuine" : "Tampered"}
                </span>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-start space-x-3">
                <Smartphone className="h-5 w-5 text-gray-400 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Scan ID:</p>
                  <p className="text-sm text-gray-900">{scanDetails.scanId}</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Calendar className="h-5 w-5 text-gray-400 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Scan Date:</p>
                  <p className="text-sm text-gray-900">{scanDetails.scanDate}</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-gray-400 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Location:</p>
                  <button
                    onClick={openGoogleMaps}
                    className="text-sm text-blue-600 hover:text-blue-800 hover:underline cursor-pointer font-medium text-left">
                    {scanDetails.location}
                  </button>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Package className="h-5 w-5 text-gray-400 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-600">QR Serial No:</p>
                  <p className="text-sm text-green-600 font-medium">{scanDetails.qrSerialNo}</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Package className="h-5 w-5 text-gray-400 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Product:</p>
                  <p className="text-sm text-gray-900">{scanDetails.product}</p>
                </div>
              </div>
              {/* 
              <div className="flex items-start space-x-3">
                <Smartphone className="h-5 w-5 text-gray-400 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Device Details:</p>
                  <p className="text-sm text-gray-900">{scanDetails.deviceDetails}</p>
                </div>
              </div> */}
            </div>
          </CardContent>
        </Card>

        {/* Map Card */}
        <Card>
          <CardHeader>
            <CardTitle>Location Map</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 rounded-lg overflow-hidden">
              {scanDetails.latitude && scanDetails.longitude ? (
                <WorldMap
                  locationData={[
                    {
                      id: 1,
                      lat: scanDetails.latitude,
                      lng: scanDetails.longitude,
                      status: scanDetails.result,
                      location: scanDetails.location,
                      date: new Date(scanDetails.scanDate).toLocaleDateString()
                    }
                  ]}
                />
              ) : (
                <div className="h-full bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <MapPin className="h-8 w-8 mx-auto mb-2" />
                    <p>Location coordinates not available</p>
                  </div>
                </div>
              )}
            </div>
            <div className="mt-4">
              <Button onClick={openGoogleMaps} variant="outline" className="w-full" disabled={!scanDetails.coordinates}>
                <MapPin className="h-4 w-4 mr-2" />
                Open in Google Maps
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ScanPage;
